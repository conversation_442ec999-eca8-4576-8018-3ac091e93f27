# Funcionalidad de Temperatura - YadConverter

## 🌡️ Monitoreo de Temperatura del CPU

YadConverter incluye un sistema de monitoreo de temperatura del CPU que se muestra en la barra superior de la aplicación.

## ⚠️ Requisitos Importantes

### Privilegios de Administrador
**Para que la temperatura funcione correctamente, la aplicación DEBE ejecutarse como administrador.**

### ¿Por qué se necesitan privilegios de administrador?
- La aplicación usa **OpenHardwareMonitor** para acceder a los sensores de temperatura del hardware
- Windows requiere privilegios elevados para acceder a los sensores de hardware
- Sin estos privilegios, solo se mostrará el **uso de CPU** como respaldo

## 🚀 Cómo Ejecutar como Administrador

### Método 1: Menú Contextual
1. Haz clic derecho en `YadConverter.py`
2. Selecciona **"Ejecutar como administrador"**
3. Confirma en el diálogo de UAC

### Método 2: PowerShell
```powershell
powershell -Command "Start-Process python -ArgumentList 'YadConverter.py' -Verb RunAs"
```

### Método 3: Usar el archivo batch incluido
```batch
run_as_admin.bat
```

## 📊 Qué Muestra el Monitor

### Con Privilegios de Administrador:
- **Temperatura real del CPU** en grados Celsius (ej: `45°C`)
- **Colores dinámicos** según la temperatura:
  - 🟢 Blanco: Normal (< 60°C)
  - 🟡 Amarillo: Tibio (60-70°C)
  - 🟠 Naranja: Caliente (70-80°C)
  - 🔴 Rojo: Muy caliente (> 80°C)

### Sin Privilegios de Administrador:
- **Uso de CPU** como respaldo (ej: `45%`)
- **Colores dinámicos** según el uso:
  - 🟢 Blanco: Normal (< 60%)
  - 🟠 Naranja: Alto (60-80%)
  - 🔴 Rojo: Muy alto (> 80%)

## 🔧 Solución de Problemas

### La temperatura no se muestra
1. **Verifica que la aplicación se ejecute como administrador**
2. Busca este mensaje en la consola:
   ```
   ADVERTENCIA: La aplicación NO se está ejecutando como administrador.
   OpenHardwareMonitor podría fallar al iniciarse.
   ```
3. Si ves este mensaje, reinicia la aplicación como administrador

### Solo se muestra uso de CPU
- Esto es normal cuando no hay privilegios de administrador
- La aplicación automáticamente usa el uso de CPU como respaldo
- Para ver la temperatura real, ejecuta como administrador

### OpenHardwareMonitor no se inicia
- Asegúrate de que el archivo `OpenHardwareMonitor/OpenHardwareMonitor.exe` existe
- Verifica que tienes privilegios de administrador
- Algunos antivirus pueden bloquear OpenHardwareMonitor

## 🛠️ Archivos de Prueba Incluidos

Para diagnosticar problemas, puedes usar estos archivos:

- `test_temperature.py` - Prueba básica de temperatura
- `test_final.py` - Prueba completa del sistema
- `test_imports.py` - Verifica que todas las dependencias estén instaladas

## 📝 Notas Técnicas

- La aplicación usa **WMI** (Windows Management Instrumentation) para acceder a los datos de OpenHardwareMonitor
- El monitoreo se actualiza cada **5 segundos** para reducir la carga del sistema
- OpenHardwareMonitor se ejecuta **completamente oculto** en segundo plano
- La aplicación limpia automáticamente los procesos de OpenHardwareMonitor al cerrarse

## ✅ Estado del Sistema

Cuando la aplicación se inicia correctamente, verás en la consola:
```
La aplicación se está ejecutando como administrador.
✓ OpenHardwareMonitor iniciado con PID: [número]
```

Si no tienes privilegios:
```
ADVERTENCIA: La aplicación NO se está ejecutando como administrador.
OpenHardwareMonitor podría fallar al iniciarse.
```
