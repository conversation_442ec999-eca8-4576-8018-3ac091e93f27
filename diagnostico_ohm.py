#!/usr/bin/env python3
"""
Diagnóstico completo de OpenHardwareMonitor
"""
import os
import sys
import subprocess
import time
import ctypes

def check_admin():
    """Verifica si se está ejecutando como administrador"""
    try:
        return ctypes.windll.shell32.IsUserAnAdmin()
    except:
        return False

def check_ohm_exists():
    """Verifica si OpenHardwareMonitor.exe existe"""
    base_path = os.path.dirname(os.path.abspath(__file__))
    ohm_path = os.path.join(base_path, 'OpenHardwareMonitor', 'OpenHardwareMonitor.exe')
    
    print(f"Buscando OpenHardwareMonitor en: {ohm_path}")
    
    if os.path.exists(ohm_path):
        print("✓ OpenHardwareMonitor.exe encontrado")
        return ohm_path
    else:
        print("❌ OpenHardwareMonitor.exe NO encontrado")
        return None

def check_ohm_running():
    """Verifica si OpenHardwareMonitor está ejecutándose"""
    try:
        import psutil
        for proc in psutil.process_iter(['name', 'pid']):
            if 'OpenHardwareMonitor.exe' in proc.info['name']:
                print(f"✓ OpenHardwareMonitor ejecutándose con PID: {proc.info['pid']}")
                return True
        print("❌ OpenHardwareMonitor NO está ejecutándose")
        return False
    except ImportError:
        print("⚠ psutil no disponible, no se puede verificar procesos")
        return False

def try_start_ohm_simple(ohm_path):
    """Intenta iniciar OpenHardwareMonitor de forma simple"""
    print("\n=== INTENTANDO INICIAR OPENHARDWAREMONITOR ===")
    
    try:
        print("Método 1: Inicio directo...")
        process = subprocess.Popen([ohm_path], 
                                 creationflags=subprocess.CREATE_NO_WINDOW)
        print(f"✓ Proceso iniciado con PID: {process.pid}")
        
        # Esperar un momento
        time.sleep(3)
        
        # Verificar si sigue ejecutándose
        if process.poll() is None:
            print("✓ Proceso sigue ejecutándose")
            return True
        else:
            print(f"❌ Proceso terminó con código: {process.returncode}")
            return False
            
    except Exception as e:
        print(f"❌ Error al iniciar: {e}")
        return False

def try_start_ohm_admin(ohm_path):
    """Intenta iniciar OpenHardwareMonitor como administrador"""
    print("\nMétodo 2: Inicio como administrador...")
    
    try:
        # Usar PowerShell para iniciar como admin
        cmd = f'Start-Process -FilePath "{ohm_path}" -Verb RunAs -WindowStyle Hidden'
        
        result = subprocess.run(['powershell', '-Command', cmd], 
                              capture_output=True, 
                              text=True,
                              timeout=10)
        
        if result.returncode == 0:
            print("✓ Comando ejecutado correctamente")
            time.sleep(5)  # Esperar más tiempo para que se inicie
            return True
        else:
            print(f"❌ Error en comando: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("⚠ Timeout en comando PowerShell")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_wmi_connection():
    """Prueba la conexión WMI a OpenHardwareMonitor"""
    print("\n=== PROBANDO CONEXIÓN WMI ===")
    
    try:
        import wmi
        print("✓ Módulo WMI disponible")
        
        # Intentar conectar
        c = wmi.WMI(namespace="root\\OpenHardwareMonitor")
        print("✓ Conexión WMI establecida")
        
        # Obtener sensores
        sensors = c.Sensor()
        print(f"✓ Encontrados {len(sensors)} sensores")
        
        # Buscar sensores de temperatura
        temp_sensors = [s for s in sensors if s.SensorType == 'Temperature']
        print(f"✓ Sensores de temperatura: {len(temp_sensors)}")
        
        if temp_sensors:
            print("Sensores de temperatura encontrados:")
            for sensor in temp_sensors[:5]:  # Mostrar solo los primeros 5
                print(f"  - {sensor.Name}: {sensor.Value}°C")
            return True
        else:
            print("❌ No se encontraron sensores de temperatura")
            return False
            
    except ImportError:
        print("❌ Módulo WMI no disponible")
        return False
    except Exception as e:
        print(f"❌ Error en WMI: {e}")
        return False

def main():
    print("=== DIAGNÓSTICO DE OPENHARDWAREMONITOR ===\n")
    
    # 1. Verificar privilegios
    is_admin = check_admin()
    print(f"1. Privilegios de administrador: {'✓ SÍ' if is_admin else '❌ NO'}")
    
    # 2. Verificar si existe el archivo
    ohm_path = check_ohm_exists()
    if not ohm_path:
        print("\n❌ PROBLEMA: OpenHardwareMonitor.exe no encontrado")
        print("   Solución: Asegúrate de que el archivo esté en la carpeta OpenHardwareMonitor/")
        return
    
    # 3. Verificar si ya está ejecutándose
    print(f"\n2. Estado actual:")
    ohm_running = check_ohm_running()
    
    # 4. Si no está ejecutándose, intentar iniciarlo
    if not ohm_running:
        if is_admin:
            # Si tenemos privilegios, intentar inicio directo
            if try_start_ohm_simple(ohm_path):
                time.sleep(3)
                check_ohm_running()
        else:
            # Si no tenemos privilegios, intentar como admin
            print("\n⚠ Sin privilegios de administrador")
            print("   Intentando iniciar con privilegios elevados...")
            if try_start_ohm_admin(ohm_path):
                time.sleep(5)
                check_ohm_running()
    
    # 5. Probar conexión WMI
    test_wmi_connection()
    
    # 6. Resumen y recomendaciones
    print(f"\n=== RESUMEN ===")
    print(f"Privilegios de admin: {'✓' if is_admin else '❌'}")
    print(f"OpenHardwareMonitor existe: {'✓' if ohm_path else '❌'}")
    print(f"OpenHardwareMonitor ejecutándose: {'✓' if check_ohm_running() else '❌'}")
    
    if not is_admin:
        print(f"\n💡 SOLUCIÓN RECOMENDADA:")
        print(f"   Para que la temperatura funcione correctamente:")
        print(f"   1. Cierra esta ventana")
        print(f"   2. Haz clic derecho en YadConverter.py")
        print(f"   3. Selecciona 'Ejecutar como administrador'")
        print(f"   4. Confirma en el diálogo de UAC")

if __name__ == "__main__":
    main()
    
    print(f"\nPresiona Enter para salir...")
    input()
