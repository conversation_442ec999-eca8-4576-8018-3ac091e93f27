import os
import shutil
import subprocess
import tempfile
import sys
import time
from PyQt6.QtWidgets import QWidget, QHBoxLayout, QLabel, QGraphicsDropShadowEffect, QFrame
from PyQt6.QtCore import QTimer, Qt, QDateTime, pyqtSignal, QObject, QThread
from PyQt6.QtGui import QFont, QColor, QPainter, QPainterPath, QBrush
from PyQt6.QtCore import QRectF

# Verificar si psutil está disponible
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False

# Verificar si wmi está disponible
try:
    import wmi
    WMI_AVAILABLE = True
except ImportError:
    WMI_AVAILABLE = False

# Verificar si win32 está disponible
try:
    import win32process
    import win32con
    import win32gui
    import ctypes
    WIN32_AVAILABLE = True
except ImportError:
    WIN32_AVAILABLE = False


class RoundedWidget(QWidget):
    """Widget personalizado con bordes redondeados dibujados manualmente"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFixedHeight(22)
        
    def paintEvent(self, event):
        """Dibuja el widget con bordes redondeados"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # Crear el path con bordes menos redondeados
        path = QPainterPath()
        rect = QRectF(self.rect())
        path.addRoundedRect(rect, 10, 10)  # Menos redondeado (8 en lugar de 12)
        
        # Dibujar el fondo
        brush = QBrush(QColor(40, 40, 40, 220))
        painter.fillPath(path, brush)


class TemperaturaWorker(QObject):
    """Worker para obtener temperatura del CPU en un hilo separado"""
    temperatura_actualizada = pyqtSignal(str)

    def __init__(self):
        super().__init__()
        self.ohm_process = None
        self.iniciar_ohm()

    def iniciar_ohm(self):
        """Inicia OpenHardwareMonitor completamente oculto con privilegios elevados"""
        try:
            if getattr(sys, 'frozen', False):
                base_path = sys._MEIPASS
            else:
                base_path = os.path.dirname(os.path.abspath(__file__))
            
            ohm_path = os.path.join(base_path, 'OpenHardwareMonitor', 'OpenHardwareMonitor.exe')
            
            if not os.path.exists(ohm_path):
                return
            
            # Verificar si ya está ejecutándose
            if PSUTIL_AVAILABLE:
                for proc in psutil.process_iter(['name']):
                    if 'OpenHardwareMonitor.exe' in proc.info['name']:
                        return  # Ya está ejecutándose
            
            try:
                # Método 1: VBS completamente oculto con privilegios elevados
                vbs_content = f'''
                Set objShell = CreateObject("WScript.Shell")
                Set UAC = CreateObject("Shell.Application")
                UAC.ShellExecute "{ohm_path}", "", "", "runas", 0
                '''

                with tempfile.NamedTemporaryFile(suffix='.vbs', delete=False, mode='w') as temp_file:
                    temp_file.write(vbs_content)
                    vbs_path = temp_file.name

                # Ejecutar VBS completamente oculto con timeout reducido
                subprocess.run(['cscript', '//Nologo', '//B', vbs_path],
                             creationflags=subprocess.CREATE_NO_WINDOW | subprocess.DETACHED_PROCESS,
                             timeout=5)

                # Limpiar archivo temporal después de un momento
                QTimer.singleShot(2000, lambda: self._cleanup_temp_file(vbs_path))
                return

            except subprocess.TimeoutExpired:
                print("Timeout al iniciar OpenHardwareMonitor con VBS")
            except Exception as e:
                print(f"Error al iniciar OpenHardwareMonitor con VBS: {e}")
            
            try:
                # Método 2: Iniciar directamente completamente oculto
                startupinfo = subprocess.STARTUPINFO()
                startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                startupinfo.wShowWindow = 0  # SW_HIDE - completamente oculto
                
                subprocess.Popen([ohm_path], 
                               startupinfo=startupinfo,
                               creationflags=subprocess.CREATE_NO_WINDOW | subprocess.DETACHED_PROCESS)
                
            except Exception:
                pass
            
        except Exception:
            pass
    
    def _cleanup_temp_file(self, file_path):
        """Limpia archivos temporales de forma segura"""
        try:
            if os.path.exists(file_path):
                os.unlink(file_path)
        except:
            pass
    
    def hide_ohm_window(self):
        """Oculta la ventana de OpenHardwareMonitor si está visible"""
        try:
            if WIN32_AVAILABLE:
                import win32gui
                import win32con
                
                def enum_windows_callback(hwnd, windows):
                    if win32gui.IsWindowVisible(hwnd):
                        window_text = win32gui.GetWindowText(hwnd)
                        if 'Open Hardware Monitor' in window_text or 'OpenHardwareMonitor' in window_text:
                            # Ocultar la ventana completamente
                            win32gui.ShowWindow(hwnd, win32con.SW_HIDE)
                    return True
                
                win32gui.EnumWindows(enum_windows_callback, [])
        except Exception:
            pass

    def cerrar_ohm(self):
        """Cierra todas las instancias de OpenHardwareMonitor"""
        try:
            if not PSUTIL_AVAILABLE:
                return False
                
            # Buscar todos los procesos de OpenHardwareMonitor
            for proc in psutil.process_iter(['pid', 'name']):
                try:
                    if 'OpenHardwareMonitor' in proc.info['name']:
                        print(f"Cerrando proceso OpenHardwareMonitor (PID: {proc.info['pid']})")
                        proc.terminate()  # Intenta cerrar suavemente
                        
                        # Espera hasta 3 segundos para que se cierre
                        gone, alive = psutil.wait_procs([proc], timeout=3)
                        
                        # Si sigue vivo después del timeout, forzar cierre
                        if alive:
                            for p in alive:
                                p.kill()
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    pass
            
            print("Todos los procesos de OpenHardwareMonitor han sido cerrados")
            return True
        except Exception as e:
            print(f"Error al cerrar OpenHardwareMonitor: {e}")
            return False

    def run(self):
        """Ejecuta el bucle principal del worker"""
        # Esperar un momento para que OHM se inicie completamente
        QThread.msleep(5000)
        
        # Ocultar ventana inicial si aparece
        self.hide_ohm_window()
        
        iteration_count = 0
        while True:
            temperatura = self.obtener_temperatura_cpu()
            self.temperatura_actualizada.emit(temperatura)
            
            # Ocultar ventana cada 5 iteraciones (cada 10 segundos)
            iteration_count += 1
            if iteration_count % 5 == 0:
                self.hide_ohm_window()
            
            QThread.msleep(2000)  # Actualiza cada 2 segundos

    def obtener_temperatura_cpu(self):
        """Obtiene la temperatura del CPU usando OpenHardwareMonitor"""
        try:
            if not WMI_AVAILABLE:
                return self.obtener_uso_cpu()
                
            # Intentar primero con OpenHardwareMonitor
            c = wmi.WMI(namespace="root\\OpenHardwareMonitor")
            temperature_infos = c.Sensor(SensorType='Temperature', Name='CPU Package')
            if temperature_infos:
                return f"CPU: {temperature_infos[0].Value:.1f}°C"
            
            # Si no hay datos de OpenHardwareMonitor, intentar con otros sensores de CPU
            temperature_infos = c.Sensor(SensorType='Temperature')
            for sensor in temperature_infos:
                if 'CPU' in sensor.Name:
                    return f"CPU: {sensor.Value:.1f}°C"
            
            # Si no hay datos de temperatura, mostrar el uso de CPU
            return self.obtener_uso_cpu()
        except Exception as e:
            # En caso de error, mostrar el uso de CPU en lugar de la temperatura
            return self.obtener_uso_cpu()

    def obtener_uso_cpu(self):
        """Obtiene el uso del CPU como respaldo"""
        try:
            if WMI_AVAILABLE:
                c = wmi.WMI()
                cpu_info = c.Win32_Processor(["LoadPercentage"])[0]
                uso = int(cpu_info.LoadPercentage)
                return f"CPU: {uso}% (uso)"
            elif PSUTIL_AVAILABLE:
                cpu_percent = psutil.cpu_percent(interval=1)
                return f"CPU: {cpu_percent:.0f}% (uso)"
            else:
                return "CPU: --% (uso)"
        except Exception as e:
            return "CPU: --% (uso)"

    def __del__(self):
        # Cerrar OpenHardwareMonitor al destruir el objeto
        self.cerrar_ohm()


class TemperaturaMonitor(QObject):
    """Monitor de temperatura que maneja el worker en un hilo separado"""
    temperatura_actualizada = pyqtSignal(str)

    def __init__(self):
        super().__init__()
        self.thread = QThread()
        self.worker = TemperaturaWorker()
        self.worker.moveToThread(self.thread)
        self.thread.started.connect(self.worker.run)
        self.worker.temperatura_actualizada.connect(self.temperatura_actualizada)
        self.thread.start()

    def cerrar(self):
        """Método para cerrar correctamente el monitor de temperatura"""
        if hasattr(self, 'worker') and self.worker:
            self.worker.cerrar_ohm()
        if hasattr(self, 'thread') and self.thread:
            self.thread.quit()
            self.thread.wait(2000)  # Esperar hasta 2 segundos para que termine

    def __del__(self):
        self.cerrar()


class SystemInfoWidget(QWidget):
    """Widget unificado que muestra hora, CPU y espacio en disco en un solo contenedor"""
    
    def __init__(self, parent=None, use_threaded_monitor=False):
        super().__init__(parent)
        self.current_path = None
        self.use_threaded_monitor = use_threaded_monitor
        self.temp_monitor = None
        self.admin_warning_shown = False

        # Verificar si se está ejecutando como administrador
        self.is_admin = self.check_admin_privileges()

        # Si se solicita usar el monitor con hilos Y se tiene privilegios de admin
        if self.use_threaded_monitor and self.is_admin:
            try:
                self.temp_monitor = TemperaturaMonitor()
                self.temp_monitor.temperatura_actualizada.connect(self.update_temp_from_monitor)
            except Exception as e:
                print(f"Error al inicializar monitor de temperatura: {e}")
                self.temp_monitor = None

        self.setup_ui()
        self.setup_timers()

    def check_admin_privileges(self):
        """Verifica si la aplicación se está ejecutando como administrador"""
        try:
            import ctypes
            return ctypes.windll.shell32.IsUserAnAdmin()
        except:
            return False

    def setup_ui(self):
        """Configura la interfaz del widget con secciones individuales"""
        self.setFixedHeight(22)  # Misma altura que los ComboBox
        
        # Estilo del contenedor principal - transparente
        self.setStyleSheet("""
            SystemInfoWidget {
                background: transparent;
                border: none;
            }
        """)
        
        # Layout horizontal principal
        main_layout = QHBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(8)  # Espacio entre secciones individuales
        
        # Sección 1: Reloj (con su propio fondo)
        self.setup_clock_section(main_layout)
        
        # Sección 2: CPU (con su propio fondo)
        self.setup_cpu_section(main_layout)
        
        # Sección 3: Disco (con su propio fondo)
        self.setup_disk_section(main_layout)
    

    
    def setup_clock_section(self, layout):
        """Configura la sección del reloj con su propio fondo redondeado"""
        clock_container = RoundedWidget()
        
        # Sin sombra para un aspecto más limpio
        
        clock_layout = QHBoxLayout(clock_container)
        clock_layout.setContentsMargins(8, 2, 8, 2)
        clock_layout.setSpacing(6)
        
        # Icono del reloj
        from CREAR import icono_RELOJ
        self.clock_icon = QLabel()
        clock_icon_pixmap = icono_RELOJ(18).pixmap(18, 18)
        self.clock_icon.setPixmap(clock_icon_pixmap)
        self.clock_icon.setStyleSheet("background: transparent; border: none;")
        
        # Etiqueta del reloj
        self.clock_label = QLabel()
        self.clock_label.setStyleSheet("""
            color: white; 
            font-size: 10px; 
            font-weight: bold;
            background: transparent;
            border: none;
        """)
        
        clock_layout.addWidget(self.clock_icon)
        clock_layout.addWidget(self.clock_label)
        
        layout.addWidget(clock_container)
    
    def setup_cpu_section(self, layout):
        """Configura la sección del CPU con su propio fondo redondeado"""
        cpu_container = RoundedWidget()
        
        # Sin sombra para un aspecto más limpio
        
        cpu_layout = QHBoxLayout(cpu_container)
        cpu_layout.setContentsMargins(8, 2, 8, 2)
        cpu_layout.setSpacing(6)
        
        # Icono del CPU
        from CREAR import icono_CPU
        self.cpu_icon = QLabel()
        cpu_icon_pixmap = icono_CPU(18).pixmap(18, 18)
        self.cpu_icon.setPixmap(cpu_icon_pixmap)
        self.cpu_icon.setStyleSheet("background: transparent; border: none;")
        
        # Etiqueta de temperatura/CPU
        self.temp_label = QLabel("--")
        self.temp_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.temp_label.setStyleSheet("""
            color: white; 
            font-size: 10px; 
            font-weight: bold;
            background: transparent;
            border: none;
        """)
        
        cpu_layout.addWidget(self.cpu_icon)
        cpu_layout.addWidget(self.temp_label)
        
        layout.addWidget(cpu_container)
    
    def setup_disk_section(self, layout):
        """Configura la sección del disco con su propio fondo redondeado"""
        disk_container = RoundedWidget()
        
        # Sin sombra para un aspecto más limpio
        
        disk_layout = QHBoxLayout(disk_container)
        disk_layout.setContentsMargins(8, 2, 8, 2)
        disk_layout.setSpacing(6)
        
        # Icono del disco
        from CREAR import icono_DISCO
        self.disk_icon = QLabel()
        disk_icon_pixmap = icono_DISCO(18).pixmap(18, 18)
        self.disk_icon.setPixmap(disk_icon_pixmap)
        self.disk_icon.setStyleSheet("background: transparent; border: none;")
        
        # Etiqueta del espacio en disco
        self.space_label = QLabel("--")
        self.space_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.space_label.setStyleSheet("""
            color: white; 
            font-size: 10px; 
            font-weight: bold;
            background: transparent;
            border: none;
        """)
        
        disk_layout.addWidget(self.disk_icon)
        disk_layout.addWidget(self.space_label)
        
        layout.addWidget(disk_container)
    
    def setup_timers(self):
        """Configura los timers para actualización con intervalos optimizados"""
        # Timer para el reloj (cada segundo) - este es ligero
        self.clock_timer = QTimer(self)
        self.clock_timer.timeout.connect(self.update_clock)
        self.clock_timer.start(1000)
        
        # Timer para CPU (cada 5 segundos para reducir carga)
        self.cpu_timer = QTimer(self)
        self.cpu_timer.timeout.connect(self.update_cpu_info_async)
        self.cpu_timer.start(5000)
        
        # Timer para disco (cada 10 segundos para reducir carga)
        self.disk_timer = QTimer(self)
        self.disk_timer.timeout.connect(self.update_disk_space_async)
        self.disk_timer.start(10000)
        
        # Timer para ocultar OpenHardwareMonitor (cada 30 segundos)
        self.hide_ohm_timer = QTimer(self)
        self.hide_ohm_timer.timeout.connect(self.hide_ohm_window)
        self.hide_ohm_timer.start(30000)
        
        # Actualizar inmediatamente solo el reloj
        self.update_clock()
        # Actualizar CPU y disco de forma asíncrona después de un delay
        QTimer.singleShot(1000, self.update_cpu_info_async)
        QTimer.singleShot(2000, self.update_disk_space_async)
    
    def get_ohm_path(self):
        """Obtiene la ruta de OpenHardwareMonitor"""
        try:
            # Obtener la ruta base de la aplicación
            base_path = os.path.dirname(os.path.abspath(__file__))
            ohm_path = os.path.join(base_path, 'OpenHardwareMonitor', 'OpenHardwareMonitor.exe')
            
            if os.path.exists(ohm_path):
                return ohm_path
            else:
                print(f"OpenHardwareMonitor no encontrado en: {ohm_path}")
                return None
        except Exception as e:
            print(f"Error al obtener ruta de OpenHardwareMonitor: {e}")
            return None
    
    def start_ohm_if_needed(self):
        """Inicia OpenHardwareMonitor si no está ejecutándose de forma completamente oculta"""
        try:
            print("Verificando si OpenHardwareMonitor está ejecutándose...")
            
            # Verificar si OHM ya está ejecutándose
            ohm_running = False
            if PSUTIL_AVAILABLE:
                for proc in psutil.process_iter(['name', 'pid']):
                    if 'OpenHardwareMonitor.exe' in proc.info['name']:
                        print(f"OpenHardwareMonitor ya está ejecutándose con PID: {proc.info['pid']}")
                        ohm_running = True
                        break
            
            if ohm_running:
                return True
            
            print("OpenHardwareMonitor no está ejecutándose, intentando iniciarlo...")
            
            # Si no está ejecutándose, iniciarlo
            ohm_path = self.get_ohm_path()
            if not ohm_path:
                print("No se pudo obtener la ruta de OpenHardwareMonitor")
                return False
            
            print(f"Ruta de OpenHardwareMonitor: {ohm_path}")
            
            # Intentar diferentes métodos de inicio
            methods = [
                ("PowerShell con privilegios elevados", self._start_with_powershell),
                ("VBS con privilegios elevados", self._start_with_vbs),
                ("Inicio directo", self._start_direct)
            ]
            
            for method_name, method_func in methods:
                try:
                    print(f"Intentando: {method_name}...")
                    if method_func(ohm_path):
                        print(f"✓ OpenHardwareMonitor iniciado con {method_name}")
                        # Ocultar la ventana si aparece
                        QTimer.singleShot(2000, self.hide_ohm_window)
                        return True
                except Exception as e:
                    print(f"Error con {method_name}: {e}")
                    continue
            
            print("Todos los métodos de inicio fallaron")
            return False
                
        except Exception as e:
            print(f"Error general al iniciar OpenHardwareMonitor: {e}")
            return False
    
    def _start_with_powershell(self, ohm_path):
        """Inicia OpenHardwareMonitor usando PowerShell con privilegios elevados"""
        try:
            powershell_cmd = f'Start-Process -FilePath "{ohm_path}" -Verb RunAs -WindowStyle Hidden'

            result = subprocess.run(
                ['powershell', '-Command', powershell_cmd],
                capture_output=True,
                text=True,
                creationflags=subprocess.CREATE_NO_WINDOW,
                timeout=10
            )

            if result.returncode == 0:
                import time
                time.sleep(3)
                return self._verify_ohm_running()

            return False

        except subprocess.TimeoutExpired:
            print("Timeout al iniciar OpenHardwareMonitor con PowerShell")
            return False
        except Exception as e:
            print(f"Error al iniciar OpenHardwareMonitor con PowerShell: {e}")
            return False
    
    def _start_with_vbs(self, ohm_path):
        """Inicia OpenHardwareMonitor usando VBS con privilegios elevados"""
        vbs_content = f'''
Set UAC = CreateObject("Shell.Application")
UAC.ShellExecute "{ohm_path}", "", "", "runas", 0
'''
        
        with tempfile.NamedTemporaryFile(suffix='.vbs', delete=False, mode='w') as temp_file:
            temp_file.write(vbs_content)
            vbs_path = temp_file.name
        
        try:
            subprocess.run(
                ['cscript', '//Nologo', vbs_path],
                creationflags=subprocess.CREATE_NO_WINDOW,
                timeout=5
            )

            import time
            time.sleep(4)

            if self._verify_ohm_running():
                QTimer.singleShot(1000, lambda: self._cleanup_temp_file(vbs_path))
                return True

            self._cleanup_temp_file(vbs_path)
            return False

        except subprocess.TimeoutExpired:
            print("Timeout al iniciar OpenHardwareMonitor con VBS")
            self._cleanup_temp_file(vbs_path)
            return False
        except Exception as e:
            print(f"Error al iniciar OpenHardwareMonitor con VBS: {e}")
            self._cleanup_temp_file(vbs_path)
            return False
    
    def _start_direct(self, ohm_path):
        """Inicia OpenHardwareMonitor directamente (sin privilegios elevados)"""
        process = subprocess.Popen(
            [ohm_path], 
            creationflags=subprocess.CREATE_NO_WINDOW | subprocess.DETACHED_PROCESS,
            stdout=subprocess.DEVNULL,
            stderr=subprocess.DEVNULL
        )
        
        import time
        time.sleep(3)
        
        return self._verify_ohm_running()
    
    def _verify_ohm_running(self):
        """Verifica si OpenHardwareMonitor está ejecutándose"""
        if PSUTIL_AVAILABLE:
            for proc in psutil.process_iter(['name', 'pid']):
                if 'OpenHardwareMonitor.exe' in proc.info['name']:
                    print(f"✓ OpenHardwareMonitor verificado con PID: {proc.info['pid']}")
                    return True
        return False
    
    def _cleanup_temp_file(self, file_path):
        """Limpia archivos temporales de forma segura"""
        try:
            if os.path.exists(file_path):
                os.unlink(file_path)
        except:
            pass
    
    def hide_ohm_window(self):
        """Oculta la ventana de OpenHardwareMonitor si está visible"""
        try:
            if WIN32_AVAILABLE:
                import win32gui
                import win32con
                
                def enum_windows_callback(hwnd, windows):
                    if win32gui.IsWindowVisible(hwnd):
                        window_text = win32gui.GetWindowText(hwnd)
                        if 'Open Hardware Monitor' in window_text or 'OpenHardwareMonitor' in window_text:
                            # Ocultar la ventana completamente
                            win32gui.ShowWindow(hwnd, win32con.SW_HIDE)
                            # También minimizar a la bandeja del sistema
                            win32gui.ShowWindow(hwnd, win32con.SW_MINIMIZE)
                    return True
                
                win32gui.EnumWindows(enum_windows_callback, [])
        except Exception:
            pass
    
    def get_cpu_temperature_ohm(self):
        """Obtiene la temperatura del CPU usando OpenHardwareMonitor"""
        try:
            print("Intentando obtener temperatura del CPU...")
            
            # Asegurarse de que OHM esté ejecutándose
            if not self.start_ohm_if_needed():
                print("No se pudo iniciar OpenHardwareMonitor")
                return None
            
            # Ocultar cualquier ventana de OpenHardwareMonitor que pueda haber aparecido
            self.hide_ohm_window()
            
            # Verificar si WMI está disponible
            if not WMI_AVAILABLE:
                print("WMI no está disponible")
                return None
            
            # Leer datos de OpenHardwareMonitor via WMI con reintentos
            import time
            max_attempts = 5
            
            for attempt in range(max_attempts):
                try:
                    print(f"Intento {attempt + 1} de {max_attempts} para obtener temperatura...")
                    
                    import wmi
                    c = wmi.WMI(namespace="root\\OpenHardwareMonitor")
                    
                    # Buscar sensores de temperatura del CPU
                    temperature_infos = c.Sensor(SensorType='Temperature')
                    
                    print(f"Encontrados {len(temperature_infos)} sensores de temperatura")
                    
                    # Priorizar CPU Package, luego Core #1, luego cualquier CPU
                    priority_names = ['CPU Package', 'Package', 'CPU Core #1', 'Core #1', 'CPU Core #0', 'Core #0']
                    
                    for priority_name in priority_names:
                        for sensor in temperature_infos:
                            if priority_name in sensor.Name:
                                temp = float(sensor.Value)
                                print(f"Sensor {sensor.Name}: {temp}°C")
                                if temp > 0 and temp < 150:  # Temperatura válida
                                    print(f"✓ Temperatura obtenida: {temp}°C")
                                    return temp
                    
                    # Si no encuentra los prioritarios, buscar cualquier sensor de CPU con valor válido
                    for sensor in temperature_infos:
                        if 'CPU' in sensor.Name or 'Core' in sensor.Name:
                            temp = float(sensor.Value)
                            print(f"Sensor alternativo {sensor.Name}: {temp}°C")
                            if temp > 0 and temp < 150:  # Temperatura válida
                                print(f"✓ Temperatura alternativa obtenida: {temp}°C")
                                return temp
                    
                    # Si todos los sensores están en 0, esperar y reintentar
                    if attempt < max_attempts - 1:
                        print(f"Todos los sensores en 0, esperando {3 + attempt} segundos...")
                        time.sleep(3 + attempt)  # Espera progresiva
                        continue
                    else:
                        print("Todos los intentos fallaron, sensores en 0")
                            
                except Exception as e:
                    print(f"Error en intento {attempt + 1}: {e}")
                    if attempt < max_attempts - 1:
                        time.sleep(2)
                        continue
                    else:
                        print("Todos los intentos de WMI fallaron")
                        return None
                
        except Exception as e:
            print(f"Error general al obtener temperatura: {e}")
            return None
        
        print("No se pudo obtener temperatura")
        return None
    
    def update_clock(self):
        """Actualiza la hora en formato AM/PM"""
        current_time = QDateTime.currentDateTime()
        time_string = current_time.toString("h:mm:ss AP")
        self.clock_label.setText(time_string)
    
    def update_temp_from_monitor(self, temp_text):
        """Actualiza la temperatura desde el monitor con hilos"""
        # Extraer temperatura del texto (formato: "CPU: 45.0°C" o "CPU: 50% (uso)")
        try:
            if "°C" in temp_text:
                # Es temperatura
                temp_str = temp_text.split(":")[1].strip().replace("°C", "")
                temp = float(temp_str)
                
                # Cambiar color según la temperatura
                if temp > 80:
                    color = "#ff5722"  # Rojo - muy caliente
                elif temp > 70:
                    color = "#ff9800"  # Naranja - caliente
                elif temp > 60:
                    color = "#ffeb3b"  # Amarillo - tibio
                else:
                    color = "rgba(255, 255, 255, 200)"  # Blanco - normal
                
                self.temp_label.setText(f"{temp:.0f}°C")
            else:
                # Es uso de CPU
                usage_str = temp_text.split(":")[1].strip().replace("% (uso)", "")
                usage = float(usage_str)
                
                if usage > 80:
                    color = "#ff5722"  # Rojo - muy alto
                elif usage > 60:
                    color = "#ff9800"  # Naranja - alto
                else:
                    color = "rgba(255, 255, 255, 200)"  # Blanco - normal
                
                self.temp_label.setText(f"{usage:.0f}%")
            
            self.temp_label.setStyleSheet(f"""
                color: {color}; 
                font-size: 11px; 
                font-weight: normal;
                background: transparent;
                border: none;
            """)
            
        except Exception as e:
            print(f"Error al procesar temperatura del monitor: {e}")
            self.temp_label.setText("--")

    def update_cpu_info_async(self):
        """Actualiza la información del CPU de forma asíncrona"""
        # Si estamos usando el monitor con hilos, no hacer nada aquí
        if self.use_threaded_monitor and self.temp_monitor:
            return

        # Si no tenemos privilegios de admin, mostrar advertencia una vez
        if not self.is_admin and not self.admin_warning_shown:
            print("ADVERTENCIA: La aplicación no se está ejecutando como administrador.")
            print("Para mostrar la temperatura del CPU, ejecuta la aplicación como administrador.")
            self.admin_warning_shown = True
        
        # Ejecutar en un hilo separado para no bloquear la UI
        from PyQt6.QtCore import QThread, QObject, pyqtSignal
        
        class CPUWorker(QObject):
            finished = pyqtSignal(str, str)  # text, color

            def __init__(self, is_admin):
                super().__init__()
                self.is_admin = is_admin

            def run(self):
                try:
                    # Solo intentar obtener temperatura si tenemos privilegios de admin
                    if self.is_admin:
                        cpu_temp = self.get_cpu_temperature_ohm()

                        if cpu_temp is not None:
                            if cpu_temp > 80:
                                color = "#ff5722"
                            elif cpu_temp > 70:
                                color = "#ff9800"
                            elif cpu_temp > 60:
                                color = "#ffeb3b"
                            else:
                                color = "rgba(255, 255, 255, 200)"

                            self.finished.emit(f"{cpu_temp:.0f}°C", color)
                            return
                    
                    # Respaldo: uso de CPU
                    if PSUTIL_AVAILABLE:
                        cpu_percent = psutil.cpu_percent(interval=0.1)  # Intervalo muy corto
                        if cpu_percent > 80:
                            color = "#ff5722"
                        elif cpu_percent > 60:
                            color = "#ff9800"
                        else:
                            color = "rgba(255, 255, 255, 200)"
                        
                        self.finished.emit(f"{cpu_percent:.0f}%", color)
                        return
                    
                    # Si todo falla
                    self.finished.emit("N/A", "rgba(255, 255, 255, 200)")
                    
                except Exception:
                    self.finished.emit("N/A", "rgba(255, 255, 255, 200)")
            
            def get_cpu_temperature_ohm(self):
                """Versión simplificada para obtener temperatura"""
                try:
                    if not WMI_AVAILABLE:
                        return None
                    
                    import wmi
                    c = wmi.WMI(namespace="root\\OpenHardwareMonitor")
                    temperature_infos = c.Sensor(SensorType='Temperature')
                    
                    for sensor in temperature_infos:
                        if 'CPU Package' in sensor.Name or 'Package' in sensor.Name:
                            temp = float(sensor.Value)
                            if temp > 0 and temp < 150:
                                return temp
                    
                    for sensor in temperature_infos:
                        if 'CPU' in sensor.Name or 'Core' in sensor.Name:
                            temp = float(sensor.Value)
                            if temp > 0 and temp < 150:
                                return temp
                                
                except Exception:
                    pass
                
                return None
        
        # Crear worker y hilo
        if not hasattr(self, 'cpu_thread') or not self.cpu_thread.isRunning():
            self.cpu_thread = QThread()
            self.cpu_worker = CPUWorker(self.is_admin)
            self.cpu_worker.moveToThread(self.cpu_thread)
            self.cpu_thread.started.connect(self.cpu_worker.run)
            self.cpu_worker.finished.connect(self.update_cpu_display)
            self.cpu_worker.finished.connect(self.cpu_thread.quit)
            self.cpu_thread.start()
    
    def update_cpu_display(self, text, color):
        """Actualiza la pantalla con los datos del CPU"""
        self.temp_label.setText(text)
        self.temp_label.setStyleSheet(f"""
            color: {color}; 
            font-size: 11px; 
            font-weight: normal;
            background: transparent;
            border: none;
        """)
    
    def update_disk_space_async(self):
        """Actualiza el espacio disponible en disco de forma asíncrona"""
        if not self.current_path:
            return
        
        # Ejecutar en un hilo separado para no bloquear la UI
        from PyQt6.QtCore import QThread, QObject, pyqtSignal
        
        class DiskWorker(QObject):
            finished = pyqtSignal(str, str)  # text, color
            
            def __init__(self, path):
                super().__init__()
                self.path = path
            
            def run(self):
                try:
                    import shutil
                    total, used, free = shutil.disk_usage(self.path)
                    
                    free_gb = free / (1024**3)
                    
                    if free_gb >= 1:
                        space_text = f"{free_gb:.1f} GB"
                    else:
                        free_mb = free / (1024**2)
                        space_text = f"{free_mb:.0f} MB"
                    
                    # Cambiar color según el espacio disponible
                    if free_gb < 1:
                        color = "#ff5722"  # Rojo
                    elif free_gb < 5:
                        color = "#ff9800"  # Naranja
                    else:
                        color = "rgba(255, 255, 255, 200)"  # Blanco
                    
                    self.finished.emit(space_text, color)
                    
                except Exception:
                    self.finished.emit("Error", "#ff5722")
        
        # Crear worker y hilo
        if not hasattr(self, 'disk_thread') or not self.disk_thread.isRunning():
            self.disk_thread = QThread()
            self.disk_worker = DiskWorker(self.current_path)
            self.disk_worker.moveToThread(self.disk_thread)
            self.disk_thread.started.connect(self.disk_worker.run)
            self.disk_worker.finished.connect(self.update_disk_display)
            self.disk_worker.finished.connect(self.disk_thread.quit)
            self.disk_thread.start()
    
    def update_disk_display(self, text, color):
        """Actualiza la pantalla con los datos del disco"""
        self.space_label.setText(text)
        self.space_label.setStyleSheet(f"""
            color: {color}; 
            font-size: 10px; 
            font-weight: bold;
            background: transparent;
            border: none;
        """)
    
    def update_disk_space(self):
        """Método mantenido por compatibilidad - redirige al asíncrono"""
        self.update_disk_space_async()
    
    def set_disk_path(self, path):
        """Establece la ruta del disco a monitorear"""
        if path and os.path.exists(path):
            self.current_path = path
            self.update_disk_space_async()
        else:
            self.current_path = None
            self.space_label.setText("--")
    
    def cleanup(self):
        """Limpia todos los timers y hilos"""
        try:
            # Detener timers
            if hasattr(self, 'clock_timer'):
                self.clock_timer.stop()
            if hasattr(self, 'cpu_timer'):
                self.cpu_timer.stop()
            if hasattr(self, 'disk_timer'):
                self.disk_timer.stop()
            if hasattr(self, 'hide_ohm_timer'):
                self.hide_ohm_timer.stop()
            
            # Limpiar hilos de CPU
            if hasattr(self, 'cpu_thread') and self.cpu_thread.isRunning():
                self.cpu_thread.quit()
                self.cpu_thread.wait(1000)
            
            # Limpiar hilos de disco
            if hasattr(self, 'disk_thread') and self.disk_thread.isRunning():
                self.disk_thread.quit()
                self.disk_thread.wait(1000)
            
            # Limpiar monitor de temperatura
            if hasattr(self, 'temp_monitor') and self.temp_monitor:
                self.temp_monitor.cerrar()
                
        except Exception as e:
            print(f"Error al limpiar SystemInfoWidget: {e}")
    
    def __del__(self):
        """Destructor para asegurar limpieza"""
        self.cleanup()
    
    def close_ohm(self):
        """Cierra OpenHardwareMonitor si está ejecutándose"""
        if not PSUTIL_AVAILABLE:
            return
        
        try:
            for proc in psutil.process_iter(['pid', 'name']):
                if 'OpenHardwareMonitor.exe' in proc.info['name']:
                    print(f"Cerrando OpenHardwareMonitor (PID: {proc.info['pid']})")
                    proc.terminate()
                    try:
                        proc.wait(timeout=3)
                    except psutil.TimeoutExpired:
                        proc.kill()
        except Exception as e:
            print(f"Error al cerrar OpenHardwareMonitor: {e}")
    
    def update_text_color(self, is_light_mode):
        """Actualiza el color del texto según el modo de interfaz"""
        # Mantener los colores dinámicos pero ajustar para el modo
        # Los colores se manejan dinámicamente en cada actualización
        pass
    
    def closeEvent(self, event):
        """Maneja el cierre del widget"""
        # Limpiar recursos
        self.cleanup()
        
        # Cerrar el monitor con hilos si existe
        if hasattr(self, 'temp_monitor') and self.temp_monitor:
            self.temp_monitor.cerrar()
        
        # Cerrar OpenHardwareMonitor
        self.close_ohm()
        
        if hasattr(super(), 'closeEvent'):
            super().closeEvent(event)