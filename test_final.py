#!/usr/bin/env python3
"""
Script de prueba final para verificar la funcionalidad de temperatura
"""
import sys
import os
import time

# Añadir el directorio actual al path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_system_widget():
    """Prueba el SystemInfoWidget con las mejoras"""
    print("=== PROBANDO SYSTEM INFO WIDGET MEJORADO ===")
    
    try:
        from PyQt6.QtWidgets import QApplication
        from PyQt6.QtCore import QTimer
        
        # Crear aplicación Qt
        app = QApplication(sys.argv)
        
        print("1. Importando SystemInfoWidget...")
        from system_info_widget import SystemInfoWidget
        
        print("2. Creando widget sin monitor con hilos...")
        widget_no_threads = SystemInfoWidget(None, use_threaded_monitor=False)
        print(f"   ✓ Widget creado - Admin: {widget_no_threads.is_admin}")
        
        print("3. Creando widget con monitor con hilos...")
        widget_with_threads = SystemInfoWidget(None, use_threaded_monitor=True)
        print(f"   ✓ Widget creado - Admin: {widget_with_threads.is_admin}")
        print(f"   Monitor con hilos: {'Activo' if widget_with_threads.temp_monitor else 'Inactivo'}")
        
        print("4. Probando actualización de CPU (sin hilos)...")
        widget_no_threads.update_cpu_info_async()
        
        # Esperar un momento para que se procese
        def check_results():
            temp_text = widget_no_threads.temp_label.text()
            print(f"   Resultado: {temp_text}")
            
            if widget_with_threads.temp_monitor:
                temp_text_threads = widget_with_threads.temp_label.text()
                print(f"   Resultado (con hilos): {temp_text_threads}")
            
            app.quit()
        
        QTimer.singleShot(3000, check_results)  # Esperar 3 segundos
        
        print("5. Ejecutando bucle de eventos por 3 segundos...")
        app.exec()
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_admin():
    """Verifica si se está ejecutando como administrador"""
    try:
        import ctypes
        is_admin = ctypes.windll.shell32.IsUserAnAdmin()
        print(f"Ejecutándose como administrador: {'Sí' if is_admin else 'No'}")
        return is_admin
    except:
        print("No se pudo verificar privilegios de administrador")
        return False

def main():
    print("PRUEBA FINAL DE FUNCIONALIDAD DE TEMPERATURA\n")
    
    # Verificar privilegios
    is_admin = check_admin()
    print()
    
    # Probar widget
    widget_ok = test_system_widget()
    
    print(f"\n=== RESUMEN ===")
    print(f"Privilegios de administrador: {'✓' if is_admin else '❌'}")
    print(f"Widget funcionando: {'✓' if widget_ok else '❌'}")
    
    if not is_admin:
        print("\n💡 SOLUCIÓN:")
        print("   Para ver la temperatura del CPU, ejecuta la aplicación como administrador:")
        print("   1. Haz clic derecho en YadConverter.py")
        print("   2. Selecciona 'Ejecutar como administrador'")
        print("   3. O usa: powershell -Command \"Start-Process python -ArgumentList 'YadConverter.py' -Verb RunAs\"")
    else:
        print("\n✅ La aplicación debería mostrar la temperatura correctamente.")

if __name__ == "__main__":
    main()
