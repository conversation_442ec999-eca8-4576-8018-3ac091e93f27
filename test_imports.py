#!/usr/bin/env python3
"""
Script para probar las importaciones básicas
"""
import sys
import os

print("Probando importaciones...")

try:
    print("1. Importando PyQt6...")
    from PyQt6.QtWidgets import QApplication, QWidget
    from PyQt6.QtCore import QTimer, Qt
    print("   ✓ PyQt6 importado correctamente")
except Exception as e:
    print(f"   ❌ Error con PyQt6: {e}")
    sys.exit(1)

try:
    print("2. Importando psutil...")
    import psutil
    print("   ✓ psutil importado correctamente")
except Exception as e:
    print(f"   ❌ Error con psutil: {e}")

try:
    print("3. Importando wmi...")
    import wmi
    print("   ✓ wmi importado correctamente")
except Exception as e:
    print(f"   ❌ Error con wmi: {e}")

try:
    print("4. Importando win32...")
    import win32process
    import win32con
    import win32gui
    print("   ✓ win32 importado correctamente")
except Exception as e:
    print(f"   ❌ Error con win32: {e}")

try:
    print("5. Probando importación de system_info_widget...")
    # Añadir el directorio actual al path
    sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
    
    # Importar solo las clases, no crear instancias
    from system_info_widget import SystemInfoWidget, TemperaturaWorker, TemperaturaMonitor
    print("   ✓ system_info_widget importado correctamente")
except Exception as e:
    print(f"   ❌ Error con system_info_widget: {e}")
    import traceback
    traceback.print_exc()

print("\nTodas las importaciones completadas.")
print("Presiona Ctrl+C para salir...")

# Mantener el script ejecutándose
try:
    import time
    while True:
        time.sleep(1)
except KeyboardInterrupt:
    print("\nScript terminado por el usuario.")
