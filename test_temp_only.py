#!/usr/bin/env python3
"""
Script para probar solo la funcionalidad de temperatura
"""
import sys
import os
import time

# Añadir el directorio actual al path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_temperatura_worker():
    """Prueba el TemperaturaWorker sin hilos"""
    print("=== PROBANDO TEMPERATURA WORKER ===")
    
    try:
        from system_info_widget import TemperaturaWorker
        
        print("1. Creando TemperaturaWorker...")
        worker = TemperaturaWorker()
        print("   ✓ Worker creado correctamente")
        
        print("2. Probando obtener_temperatura_cpu...")
        temperatura = worker.obtener_temperatura_cpu()
        print(f"   Resultado: {temperatura}")
        
        print("3. Probando obtener_uso_cpu...")
        uso_cpu = worker.obtener_uso_cpu()
        print(f"   Resultado: {uso_cpu}")
        
        print("4. Cerrando worker...")
        worker.cerrar_ohm()
        print("   ✓ Worker cerrado")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_temperatura_simple():
    """Prueba obtener temperatura de forma simple"""
    print("\n=== PROBANDO TEMPERATURA SIMPLE ===")
    
    try:
        # Verificar si wmi está disponible
        try:
            import wmi
            WMI_AVAILABLE = True
            print("1. WMI disponible")
        except ImportError:
            WMI_AVAILABLE = False
            print("1. WMI NO disponible")
            return False
        
        print("2. Intentando conectar a OpenHardwareMonitor...")
        c = wmi.WMI(namespace="root\\OpenHardwareMonitor")
        print("   ✓ Conexión establecida")
        
        print("3. Obteniendo sensores de temperatura...")
        temperature_infos = c.Sensor(SensorType='Temperature')
        print(f"   Encontrados {len(temperature_infos)} sensores")
        
        if len(temperature_infos) == 0:
            print("   ⚠ No hay sensores disponibles (OpenHardwareMonitor no está ejecutándose)")
            return False
        
        print("4. Listando sensores:")
        for sensor in temperature_infos:
            print(f"   - {sensor.Name}: {sensor.Value}°C")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

def test_cpu_usage():
    """Prueba obtener uso de CPU como respaldo"""
    print("\n=== PROBANDO USO DE CPU ===")
    
    try:
        import psutil
        print("1. psutil disponible")
        
        print("2. Obteniendo uso de CPU...")
        cpu_percent = psutil.cpu_percent(interval=1)
        print(f"   Uso de CPU: {cpu_percent}%")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

def main():
    print("PRUEBA DE FUNCIONALIDAD DE TEMPERATURA\n")
    
    # Probar uso de CPU (siempre debería funcionar)
    test_cpu_usage()
    
    # Probar temperatura simple
    temp_simple_ok = test_temperatura_simple()
    
    # Probar worker
    worker_ok = test_temperatura_worker()
    
    print(f"\n=== RESUMEN ===")
    print(f"Temperatura simple: {'✓' if temp_simple_ok else '❌'}")
    print(f"Worker: {'✓' if worker_ok else '❌'}")
    
    if not temp_simple_ok:
        print("\n💡 SUGERENCIA: Ejecuta la aplicación como administrador para que OpenHardwareMonitor funcione.")
        print("   Usa: 'Ejecutar como administrador' en el menú contextual del archivo .py")

if __name__ == "__main__":
    main()
