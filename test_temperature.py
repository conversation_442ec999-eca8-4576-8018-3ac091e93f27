#!/usr/bin/env python3
"""
Script de prueba para verificar la funcionalidad de temperatura
"""
import sys
import os
import time

# Verificar si psutil está disponible
try:
    import psutil
    PSUTIL_AVAILABLE = True
    print("✓ psutil disponible")
except ImportError:
    PSUTIL_AVAILABLE = False
    print("✗ psutil NO disponible")

# Verificar si wmi está disponible
try:
    import wmi
    WMI_AVAILABLE = True
    print("✓ wmi disponible")
except ImportError:
    WMI_AVAILABLE = False
    print("✗ wmi NO disponible")

def verificar_ohm():
    """Verifica si OpenHardwareMonitor está ejecutándose"""
    if not PSUTIL_AVAILABLE:
        print("No se puede verificar OHM sin psutil")
        return False
    
    for proc in psutil.process_iter(['name', 'pid']):
        if 'OpenHardwareMonitor.exe' in proc.info['name']:
            print(f"✓ OpenHardwareMonitor ejecutándose con PID: {proc.info['pid']}")
            return True
    
    print("✗ OpenHardwareMonitor NO está ejecutándose")
    return False

def obtener_temperatura():
    """Intenta obtener la temperatura del CPU"""
    if not WMI_AVAILABLE:
        print("WMI no disponible, no se puede obtener temperatura")
        return None
    
    try:
        print("Intentando conectar a OpenHardwareMonitor via WMI...")
        c = wmi.WMI(namespace="root\\OpenHardwareMonitor")
        
        print("Obteniendo sensores de temperatura...")
        temperature_infos = c.Sensor(SensorType='Temperature')
        
        print(f"Encontrados {len(temperature_infos)} sensores de temperatura:")
        
        for sensor in temperature_infos:
            print(f"  - {sensor.Name}: {sensor.Value}°C")
            if 'CPU Package' in sensor.Name or 'Package' in sensor.Name:
                temp = float(sensor.Value)
                if temp > 0 and temp < 150:
                    print(f"✓ Temperatura CPU obtenida: {temp}°C")
                    return temp
        
        # Si no encuentra CPU Package, buscar cualquier sensor de CPU
        for sensor in temperature_infos:
            if 'CPU' in sensor.Name or 'Core' in sensor.Name:
                temp = float(sensor.Value)
                if temp > 0 and temp < 150:
                    print(f"✓ Temperatura CPU alternativa obtenida: {temp}°C")
                    return temp
        
        print("✗ No se encontraron sensores de CPU válidos")
        return None
        
    except Exception as e:
        print(f"✗ Error al obtener temperatura: {e}")
        return None

def iniciar_ohm():
    """Intenta iniciar OpenHardwareMonitor"""
    base_path = os.path.dirname(os.path.abspath(__file__))
    ohm_path = os.path.join(base_path, 'OpenHardwareMonitor', 'OpenHardwareMonitor.exe')
    
    print(f"Ruta de OpenHardwareMonitor: {ohm_path}")
    
    if not os.path.exists(ohm_path):
        print(f"✗ OpenHardwareMonitor.exe no encontrado en {ohm_path}")
        return False
    
    print("✓ OpenHardwareMonitor.exe encontrado")
    
    try:
        import subprocess
        print("Intentando iniciar OpenHardwareMonitor...")
        
        # Iniciar de forma oculta
        startupinfo = subprocess.STARTUPINFO()
        startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
        startupinfo.wShowWindow = 0  # SW_HIDE
        
        process = subprocess.Popen([ohm_path], 
                                 startupinfo=startupinfo,
                                 creationflags=subprocess.CREATE_NO_WINDOW | subprocess.DETACHED_PROCESS)
        
        print(f"✓ OpenHardwareMonitor iniciado con PID: {process.pid}")
        
        # Esperar un momento para que se inicie
        print("Esperando 5 segundos para que OHM se inicie...")
        time.sleep(5)
        
        return True
        
    except Exception as e:
        print(f"✗ Error al iniciar OpenHardwareMonitor: {e}")
        return False

def main():
    print("=== PRUEBA DE FUNCIONALIDAD DE TEMPERATURA ===\n")
    
    print("1. Verificando dependencias...")
    print()
    
    print("2. Verificando si OpenHardwareMonitor está ejecutándose...")
    ohm_running = verificar_ohm()
    print()
    
    if not ohm_running:
        print("3. Intentando iniciar OpenHardwareMonitor...")
        if iniciar_ohm():
            print("Verificando nuevamente si está ejecutándose...")
            verificar_ohm()
        print()
    
    print("4. Intentando obtener temperatura...")
    temperatura = obtener_temperatura()
    print()
    
    if temperatura:
        print(f"🎉 ÉXITO: Temperatura obtenida: {temperatura}°C")
    else:
        print("❌ FALLO: No se pudo obtener la temperatura")
        
        # Intentar obtener uso de CPU como respaldo
        if PSUTIL_AVAILABLE:
            try:
                cpu_percent = psutil.cpu_percent(interval=1)
                print(f"📊 Respaldo - Uso de CPU: {cpu_percent}%")
            except Exception as e:
                print(f"❌ Error al obtener uso de CPU: {e}")

if __name__ == "__main__":
    main()
