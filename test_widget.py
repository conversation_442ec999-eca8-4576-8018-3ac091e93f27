#!/usr/bin/env python3
"""
Script de prueba para el widget de temperatura
"""
import sys
import os
from PyQt6.QtWidgets import QApplication, QWidget, QVBoxLayout, QLabel
from PyQt6.QtCore import QTimer
from PyQt6.QtGui import QFont

# Añadir el directorio actual al path para importar los módulos
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    app = QApplication(sys.argv)
    
    # Crear ventana principal
    window = QWidget()
    window.setWindowTitle("Prueba de Widget de Temperatura")
    window.setGeometry(100, 100, 400, 200)
    
    layout = QVBoxLayout()
    
    # Etiqueta de información
    info_label = QLabel("Probando widget de temperatura...")
    info_label.setFont(QFont("Arial", 12))
    layout.addWidget(info_label)
    
    try:
        # Importar y crear el widget de sistema
        from system_info_widget import SystemInfoWidget
        
        # Crear widget sin monitor con hilos
        system_widget = SystemInfoWidget(window, use_threaded_monitor=False)
        layout.addWidget(system_widget)
        
        info_label.setText("✓ Widget de temperatura cargado correctamente")
        
        # Timer para actualizar la información cada 5 segundos
        def update_info():
            info_label.setText(f"Widget funcionando - Temperatura: {system_widget.temp_label.text()}")
        
        timer = QTimer()
        timer.timeout.connect(update_info)
        timer.start(5000)
        
    except Exception as e:
        info_label.setText(f"❌ Error al cargar widget: {str(e)}")
        print(f"Error detallado: {e}")
        import traceback
        traceback.print_exc()
    
    window.setLayout(layout)
    window.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
